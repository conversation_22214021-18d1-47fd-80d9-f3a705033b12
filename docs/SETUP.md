# Setup Guide

## Prerequisites

- Python 3.8 or higher
- OpenAI API key
- Pinecone account and API key
- Neo4j database (Aura or local instance)

## Installation

1. **Clone the repository**:
   ```bash
   git clone https://github.com/yourusername/rag-llm-fact-reranker.git
   cd rag-llm-fact-reranker
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and database credentials
   ```

4. **Update configuration**:
   ```bash
   cp config/config.yaml.example config/config.yaml
   # Edit config.yaml with your specific settings
   ```

## Database Setup

### Neo4j Setup
1. Create a Neo4j Aura instance or set up a local Neo4j database
2. Note the connection URI, username, and password
3. Update the Neo4j credentials in your configuration

### Pinecone Setup
1. Create a Pinecone account at https://pinecone.io
2. Create a new index with:
   - Dimension: 1536 (for text-embedding-ada-002)
   - Metric: cosine
3. Note your API key and environment
4. Update the Pinecone credentials in your configuration

## Running the System

### Build Knowledge Base
```bash
python scripts/run_system.py --build-kb --subset-size 100
```

### Query the System
```bash
python scripts/run_system.py --query "How old was Madonna when she moved to New York?"
```

### Run Evaluation
```bash
python tests/evaluate_system.py
```

## Troubleshooting

- **API Rate Limits**: The system includes retry logic for API calls
- **Memory Issues**: Reduce subset size when building knowledge base
- **Connection Errors**: Verify database credentials and network connectivity
