# Improving the Fact-Centric Knowledge Web with GPT-Based Fact Re-Ranking

This project is an enhancement of the paper **"Fact-Centric Knowledge Web for Information Retrieval"** presented at the **IEEE/WIC International Conference on Web Intelligence and Intelligent Agent Technology (WI-IAT 2024)**.

By integrating a **GPT-based fact re-ranking module** into the original retrieval-augmented generation (RAG) system, this project improves multi-hop question answering accuracy by 20%, avoids hallucinations, and enables reasoning through adaptive prompting.


## 🔧 Key Features

- **Paper-Based Reproduction**: Full re-implementation of the Fact-Centric Knowledge Web (FCKW) system from WI-IAT 2024.
- **LLM Integration**: Custom GPT-3.5 fact re-ranker module tailored to question types (inference, numeric reasoning, etc).
- **Performance Boost**: Improved Approximate Match Accuracy from **65.7% to 85%** on MuSiQue dataset.
- **Semantic Graph Search**: Utilized Pinecone vector search + Neo4j knowledge graph to retrieve semantically relevant facts.
- **Hallucination Mitigation**: The system gracefully rejects incorrect answers when supporting facts are missing.


## 🛠️ Tech Stack

- `Python`
- `OpenAI GPT-3.5 API`
- `Neo4j` (for graph storage)
- `Pinecone` (vector database for keyword embedding)
- `MuSiQue` (multi-hop QA benchmark dataset)
- `Prompt Engineering` (query-type based prompt construction)

## 📈 Results

| Metric                  | Baseline (FCKW) | With GPT Re-Ranker |
|------------------------|------------------|---------------------|
| Approximate Match Accuracy | 65.7%            | **85.0%**            |

Examples show improved answer accuracy and avoidance of hallucination on complex multi-hop reasoning tasks.


---

## 🔍 Before & After: Real Example Cases

These examples demonstrate how the GPT-based re-ranking module improved the factual precision and reasoning ability of the system — even without any changes to the knowledge graph itself.

---

**Example 1: Numerical Reasoning**  
**Question:** How old was Yale Law School when it celebrated its centennial?  
- **Baseline Answer:** *Yale Law School celebrated its centennial in 1943.*  
- **Improved Answer:** *Yale Law School celebrated its centennial in 1943, making it 100 years old at the time of the celebration.* ✅  
- **Why it matters:** The baseline retrieved the correct year but failed to do the math; the re-ranker explicitly supports reasoning-based prompts.

---

**Example 2: Entity Disambiguation / Hallucination Prevention**  
**Question:** Which Ivy League school educated the future Zappos investor Tony Hsieh?  
- **Baseline Answer:** *Yale University educated the future Zappos investor Tony Hsieh.* ❌ (hallucinated)  
- **Improved Answer:** *The facts provided do not contain information about which Ivy League school educated Tony Hsieh.* ✅  
- **Why it matters:** The improved system avoids hallucinating unsupported claims by prompting the LLM to acknowledge missing evidence.

---

**Example 3: Date Calculation**  
**Question:** How old was Madonna when she moved to New York in 1977?  
- **Baseline Answer:** *No specific information about Madonna’s age in 1977 was found.*  
- **Improved Answer:** *Madonna was born in 1958, so she would have been 19 years old when she moved to New York City in 1977.* ✅  
- **Why it matters:** Shows that reasoning over partial facts (birth year + event year) can be enabled with question-type-aware prompting.



## 🚀 Getting Started

1. Clone this repo:
```bash
git clone https://github.com/yourusername/rag-llm-fact-reranker.git
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Configure your `.env` file with:
- OpenAI API Key
- Pinecone API Key + Environment
- Neo4j DB URI, Username, Password

4. Run the pipeline:
```bash
python run_reranker.py --question "How old was Madonna when she moved to New York?"
```

## 📚 Reference

Based on the paper:  
**"Fact-Centric Knowledge Web for Information Retrieval"**  
A. Sinha & Y. Shiramatsu, IEEE/WIC WI-IAT 2024  
[Link to Paper](https://pure.nitech.ac.jp/en/publications/fact-centric-knowledge-web-for-information-retrieval)

This repo was created as a research-driven ML project by **Murun Enkhtaivan**, M.S. in Data Science, graduating December 2025.
