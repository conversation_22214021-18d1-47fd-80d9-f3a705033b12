openai_api_key: "********************************************************************************************************************************************************************"

pinecone_api_key: "pcsk_5irPfw_7Zeb5VkmYwRaAR1mF7RicffDmNLeiQGu6vQe5yzr5c1Gt8quh8fQQFLC6uJnG8b"
pinecone_env: "gcp-starter"       # Pinecone environment (e.g., "gcp-starter")
pinecone_index: "knowledge-web-index"  # Must be created in Pinecone dashboard

neo4j_uri: "neo4j+s://a22560c3.databases.neo4j.io"
neo4j_user: "neo4j"
neo4j_password: "PZgqyJTj4HhCzhTQR9Kjw6e7ye5RSeDu0mMjMUODltQ"
AURA_INSTANCEID: "a22560c3"
AURA_INSTANCENAME: "Instance01"

embedding_model: "text-embedding-ada-002"
retrieval_top_k: 5
